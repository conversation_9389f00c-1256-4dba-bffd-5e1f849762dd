{"name": "nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"lucide-react": "^0.456.0", "motion": "^12.4.3", "next": "14.2.11", "next-themes": "^0.4.3", "react": "^18", "react-dom": "^18"}, "devDependencies": {"@eslint/js": "^9.15.0", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^9.15.0", "eslint-config-next": "14.2.11", "eslint-plugin-react": "^7.37.2", "globals": "^15.12.0", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5", "typescript-eslint": "^8.16.0"}}