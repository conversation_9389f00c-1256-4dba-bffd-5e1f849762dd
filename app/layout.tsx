import React from "react";
import type { <PERSON>ada<PERSON> } from "next";
import localFont from "next/font/local";
import { Lora, Inter } from "next/font/google";
import "./globals.css";
import { Providers } from "@/app/providers";
import TopNav from "@/app/components/topnav";

const geistSans = localFont({
  src: "./fonts/GeistVF.woff",
  variable: "--font-geist-sans",
  weight: "100 900",
});
const geistMono = localFont({
  src: "./fonts/GeistMonoVF.woff",
  variable: "--font-geist-mono",
  weight: "100 900",
});

// Lora for headings
const lora = Lora({
  subsets: ["latin"],
  variable: "--font-lora",
  weight: ["400", "500", "600", "700"],
});

// Using Inter as a close alternative to Proxima Nova (which isn't available on Google Fonts)
const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  weight: ["300", "400", "500", "600"],
});

export const metadata: Metadata = {
  title: "nthpaul.com",
  icons: [
    {
      rel: "icon",
      url: "/static/favicons/favicon.ico",
    },
  ],
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} ${lora.variable} ${inter.variable} antialiased transition-[height] duration-300 ease-in-out`}
      >
        <Providers>
          <TopNav />
          <div className="font-body dark:text-white text-blue-800 bg-white dark:bg-gray-900 flex justify-center pt-16">
            {children}
          </div>
        </Providers>
      </body>
    </html>
  );
}
