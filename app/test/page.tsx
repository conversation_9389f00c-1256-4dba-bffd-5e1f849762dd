'use client'

import * as motion from 'motion/react-client'
import React from 'react';

const box = {
  width: 100,
  height: 100,
  background: 'red',
  borderRadius: 5
}

const TestPage = () => {
  return (
    <div className='h-[500px] flex items-center justify-center'>
      <motion.div
        style={box}
        animate={{
          rotate: 360,
          scale: 2
        }}
        transition={{ duration: 1 }}
      >

      </motion.div>
    </div>
  )
}

export default TestPage;
