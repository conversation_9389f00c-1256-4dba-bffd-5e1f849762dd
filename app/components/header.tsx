import React from "react";
import Link from "next/link";

const Header: React.FC = () => {
  return (
    <header className="text-md flex gap-5 items-center">
      <a
        href="mailto:<EMAIL>"
        className="w-full flex justify-start underline"
      >
        <EMAIL>
      </a>
      <Link
        className="hover:bg-blue-100 hover:text-blue-900 dark:hover:bg-gray-700 dark:hover:text-white"
        href="https://github.com/nthpaul/"
        // target="_blank"
      >
        [github]
      </Link>
      <Link
        className="hover:bg-blue-100 hover:text-blue-900 dark:hover:bg-gray-700 dark:hover:text-white"
        href="https://x.com/nthpaul"
      >
        [x]
      </Link>
    </header>
  );
};

export default Header;
