"use client";
import React from "react";
import { usePathname } from "next/navigation";
import { ThemeToggle } from "@components/theme-toggle";

const TopNav: React.ComponentType = () => {
  return (
    <nav className="flex justify-between items-center p-2 bg-blue-800 dark:bg-white w-full bg-opacity-5 dark:bg-opacity-5 mb-8">
      <div className="flex items-center space-x-4">
        <NavItem text="home" href="/" />
        <NavItem text="articles" href="/articles" />
      </div>
      <div className="ml-8">
        <ThemeToggle />
      </div>
    </nav>
  );
};

export default TopNav;

const NavItem: React.FC<{ text: string; href: string }> = ({ text, href }) => {
  const pathname = usePathname();
  const isActive =
    pathname === href || (href !== "/" && pathname?.startsWith(href));

  return (
    <a
      href={href}
      className={`hover:bg-blue-100 hover:text-blue-900 dark:hover:bg-gray-700 dark:hover:text-white transition-colors p-1 py-[6px] ${
        isActive
          ? "bg-blue-800 dark:bg-white bg-opacity-5 dark:bg-opacity-5"
          : ""
      }`}
    >
      {text}
    </a>
  );
};
