"use client";

import React, { useState, useEffect } from "react";

interface TypewriterProps {
  texts: string[];
  typingDelay?: number;
  deletingDelay?: number;
  pauseDelay?: number;
  infinite?: boolean;
  once?: boolean;
  className?: string;
}

const Typewriter: React.FC<TypewriterProps> = ({
  texts,
  typingDelay = 127,
  deletingDelay = 127,
  pauseDelay = 500,
  infinite = false,
  once = false,
  className
}) => {
  const [currentText, setCurrentText] = useState("");
  const [currentIndex, setCurrentIndex] = useState(0);
  const [currentTextIndex, setCurrentTextIndex] = useState(0);
  const [isDeleting, setIsDeleting] = useState(false);

  const currentFullText = texts[currentTextIndex];

  useEffect(() => {
    let timeout: NodeJS.Timeout;

    if (once) {
      if (!isDeleting && currentIndex < currentFullText.length) {
        timeout = setTimeout(() => {
          setCurrentText((prev) => prev + currentFullText[currentIndex]);
          setCurrentIndex((prev) => prev + 1);
        }, typingDelay);
      }
    } else {
      if (!isDeleting && currentIndex < currentFullText.length) {
        timeout = setTimeout(() => {
          setCurrentText((prev) => prev + currentFullText[currentIndex]);
          setCurrentIndex((prev) => prev + 1);
        }, typingDelay);
      } else if (!isDeleting && currentIndex === currentFullText.length) {
        timeout = setTimeout(() => {
          setIsDeleting(true);
        }, pauseDelay);
      } else if (isDeleting && currentIndex > 0) {
        timeout = setTimeout(() => {
          setCurrentText((prev) => prev.slice(0, -1));
          setCurrentIndex((prev) => prev - 1);
        }, deletingDelay);
      } else if (isDeleting && currentIndex === 0) {
        const nextTextIndex = (currentTextIndex + 1) % texts.length;
        if (infinite || nextTextIndex !== 0) {
          timeout = setTimeout(() => {
            setCurrentTextIndex(nextTextIndex);
            setIsDeleting(false);
          }, pauseDelay);
        }
      }
    }

    return () => clearTimeout(timeout);
  }, [
    currentIndex,
    currentTextIndex,
    isDeleting,
    typingDelay,
    deletingDelay,
    pauseDelay,
    infinite,
    texts,
    currentFullText,
  ]);

  if (!currentText) {
    return <p className={className + ' opacity-0 w-full'}>a random string of words</p>
  }

  if (once) {
    return (
      <p className={className}>
        {currentText}
      </p>
    )
  }

  return (
    <p className={className}>
      <span className="opacity-0">X</span>
      {currentText.toLowerCase()}
      <span className="opacity-0">X</span>
    </p>
  );
};

export default Typewriter;
