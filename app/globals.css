@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --foreground: #1e3a8a;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #1a1a1a;
    --foreground: #ffffff;
  }
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: var(--font-inter), sans-serif;
}

/* Typography: Headings use Lora, content uses Inter (Proxima Nova alternative) */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--font-lora), serif;
  font-weight: 600;
}

h1 {
  font-size: 2.25rem; /* 36px */
  line-height: 2.5rem; /* 40px */
}

h2 {
  font-size: 1.875rem; /* 30px */
  line-height: 2.25rem; /* 36px */
}

h3 {
  font-size: 1.5rem; /* 24px */
  line-height: 2rem; /* 32px */
}

h4 {
  font-size: 1.25rem; /* 20px */
  line-height: 1.75rem; /* 28px */
}

h5 {
  font-size: 1.125rem; /* 18px */
  line-height: 1.75rem; /* 28px */
}

h6 {
  font-size: 1rem; /* 16px */
  line-height: 1.5rem; /* 24px */
}

p,
span,
div,
a,
li {
  font-family: var(--font-inter), sans-serif;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

#cursor {
  width: 10px;
  height: 10px;
  background: var(--background);
  border-radius: 50%;
  position: fixed;
  pointer-events: none;
  mix-blend-mode: screen;
  box-shadow: 0 0 20px var(--background);
}

.matrix-char {
  position: absolute;
  pointer-events: none;
  font-family: monospace;
  font-size: 20px;
  color: var(--background);
  transition: opacity 1s;
}
