"use client";

import React from "react";
import Typewriter from "@components/typewriter";
import TopNav from "@components/topnav";
import { SquareArrowUpRight } from "lucide-react";

const Home: React.FC = () => {
  return (
    <div className="flex flex-1 flex-col items-center justify-center min-h-screen pb-20 gap-16 sm:py-20 font-body dark:text-[#9DBE7F] text-gray-700 bg-white dark:bg-[#252525] max-w-[25rem] dark:">
      <main className="flex flex-col items-start justify-center gap-8 sm:gap-16 w-full relative top-8 sm:top-0">
        <div className="flex flex-col items-start gap-2">
          <TopNav />
          <div className="flex items-center justify-start gap-2">
            <Typewriter
              texts={[`Articles`]}
              once
              className="text-xl font-heading"
              typingDelay={100}
            />
            <SquareArrowUpRight className="inline-block ml-1 h-6 w-6 animate-fade-in opacity-0" />
          </div>
          <div className="space-y-4">
            <p className="text-sm">{`Pieces I've read and enjoyed. I didn't write these.`}</p>
          </div>
          <section className="mt-6">
            <ul className="list-disc pl-5">
              <ListItem
                text="Mutable vs. Immutable Infrastructure"
                href="https://www.hashicorp.com/en/resources/what-is-mutable-vs-immutable-infrastructure?product_intent=terraform"
              />
            </ul>
          </section>
        </div>
      </main>
    </div>
  );
};

export default Home;

const ListItem: React.FC<{ text: string; href: string }> = ({ text, href }) => {
  return (
    <li className="list-disc pl-3">
      <a
        href={href}
        target="_blank"
        rel="noopener noreferrer"
        className="hover:bg-gray-700 hover:text-white dark:hover:bg-[#3b4830] transition-colors flex items-center"
      >
        {text}
      </a>
    </li>
  );
};
