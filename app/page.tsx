"use client";

import React from "react";
import Typewriter from "@components/typewriter";
import Header from "@components/header";
import { DarkPfp } from "@components/ascii-art/dark-pfp";
import { LightPfp } from "@components/ascii-art/light-pfp";
import TopNav from "@components/topnav";

const Home: React.FC = () => {
  return (
    <div className="flex flex-1 flex-col items-center justify-center min-h-screen pb-20 gap-16 sm:py-20 font-body dark:text-[#9DBE7F] text-gray-700 bg-white dark:bg-[#252525] max-w-[25rem] dark:">
      <main className="flex flex-col items-start justify-center gap-8 w-full relative top-8 sm:top-0">
        <div className='flex flex-col items-start gap-2'>
          <TopNav />
          <div className='flex items-center justify-start gap-2'>
            <Typewriter texts={[`Hi, I'm <PERSON>`]} once className='text-xl font-heading' typingDelay={70} />
          </div>
          <div className='space-y-4'>
            <p className='text-sm'>
              {`I'm a software engineer at a fintech startup.`}
            </p>
            <div className='flex justify-between w-full'>
              <div className="hidden sm:dark:block">
                <DarkPfp />
              </div>
              <div className="sm:block dark:hidden hidden">
                <LightPfp />
              </div>
            </div>
          </div>
        </div>
        <Header />
      </main >
    </div >
  );
};

export default Home;
